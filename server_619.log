nohup: ignoring input
2025-07-29 20:39:19,332 - app.main - INFO - Request logging middleware initialized and ready to capture requests
INFO:     Started server process [46799]
INFO:     Waiting for application startup.
2025-07-29 20:39:19,336 - app.main - INFO - Initializing application
2025-07-29 20:39:19,337 - databases - INFO - Connected to database sqlite+aiosqlite:///./sql_app.db
2025-07-29 20:39:19,337 - app.main - INFO - Async database connected successfully
2025-07-29 20:39:19,337 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:39:19,337 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:39:19,337 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("mcc_analysis_gemini")
2025-07-29 20:39:19,337 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("mcc_analysis_gemini")
2025-07-29 20:39:19,337 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:39:19,337 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("website_urls_gemini")
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("website_urls_gemini")
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("general_logs_gemini")
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("general_logs_gemini")
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("websites_gemini")
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("websites_gemini")
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("mcc_url_classification_gemini")
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("mcc_url_classification_gemini")
2025-07-29 20:39:19,338 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:39:19,338 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:39:19,339 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 20:39:19,339 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 20:39:19,339 - app.main - INFO - Database tables initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
2025-07-29 20:39:31,902 - app.routers.mcc_analysis - INFO - Processing MCC analysis request for https://drynotch.com with ref_id 208378df-fasdf32-sdfgcfeddcd
[2025-07-29 20:39:31][request_middleware][NO_REF] INFO: Request started: POST /mcc-analysis/
{
  "request_id": "f2b52979-88ef-4082-8cf2-c8c301d26568",
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
  "query_params": null,
  "headers": {
    "host": "127.0.0.1:8000",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "accept": "application/json",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br, zstd",
    "referer": "http://127.0.0.1:8000/docs",
    "content-type": "application/json",
    "content-length": "3179",
    "origin": "http://127.0.0.1:8000",
    "connection": "keep-alive",
    "cookie": "username-127-0-0-1-8888=\"2|1:0|10:1753687981|23:username-127-0-0-1-8888|44:OTI1NWYwNDA2MmYzNGM4ODhmZWEyMzU0NDNhZWYzZGU=|af37f3c5748e269f0b3a691141a8d9d34952092469c0df3eaa2fabbf0681f48d\"; _xsrf=2|a122885f|aa0d74d2af0b3aa32b5dde71a04b1553|1753687981",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "priority": "u=0"
  },
  "url_full": "http://127.0.0.1:8000/mcc-analysis/"
}
2025-07-29 20:39:31,903 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:39:31,903 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:39:31,911 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 20:39:31,911 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 20:39:31,911 INFO sqlalchemy.engine.Engine [generated in 0.00030s] ('208378df-fasdf32-sdfgcfeddcd',)
2025-07-29 20:39:31,911 - sqlalchemy.engine.Engine - INFO - [generated in 0.00030s] ('208378df-fasdf32-sdfgcfeddcd',)
2025-07-29 20:39:31,912 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:39:31,912 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:39:31,914 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 20:39:31,914 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 20:39:31,914 INFO sqlalchemy.engine.Engine [generated in 0.00016s] ('208378df-fasdf32-sdfgcfeddcd',)
2025-07-29 20:39:31,914 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ('208378df-fasdf32-sdfgcfeddcd',)
2025-07-29 20:39:31,920 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,920 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,920 INFO sqlalchemy.engine.Engine [generated in 0.00028s (insertmanyvalues) 1/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,920 - sqlalchemy.engine.Engine - INFO - [generated in 0.00028s (insertmanyvalues) 1/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,920 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,920 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,920 INFO sqlalchemy.engine.Engine [insertmanyvalues 2/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,920 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 2/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,920 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,920 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,920 INFO sqlalchemy.engine.Engine [insertmanyvalues 3/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,920 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 3/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,920 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,920 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,920 INFO sqlalchemy.engine.Engine [insertmanyvalues 4/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,920 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 4/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,920 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,920 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,920 INFO sqlalchemy.engine.Engine [insertmanyvalues 5/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/top-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,920 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 5/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/top-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine [insertmanyvalues 6/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 6/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine [insertmanyvalues 7/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/size-chart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 7/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/size-chart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine [insertmanyvalues 8/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 8/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine [insertmanyvalues 9/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 9/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine [insertmanyvalues 10/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/co-ord-sets', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 10/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/co-ord-sets', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine [insertmanyvalues 11/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/contact', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 11/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/contact', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine [insertmanyvalues 12/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 12/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine [insertmanyvalues 13/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/work-it-out-straight-fit-pants', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 13/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/work-it-out-straight-fit-pants', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,921 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,921 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine [insertmanyvalues 14/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/yoga-lite-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 14/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/yoga-lite-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine [insertmanyvalues 15/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/best-sellers', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 15/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/best-sellers', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine [insertmanyvalues 16/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/account/login', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 16/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/account/login', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine [insertmanyvalues 17/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 17/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine [insertmanyvalues 18/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/mauve-mist-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 18/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/mauve-mist-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine [insertmanyvalues 19/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Co-ords', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 19/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Co-ords', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine [insertmanyvalues 20/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/frontpage', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 20/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/frontpage', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine [insertmanyvalues 21/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 21/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,922 INFO sqlalchemy.engine.Engine [insertmanyvalues 22/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/navy-nirvana-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,922 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 22/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/navy-nirvana-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine [insertmanyvalues 23/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 23/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine [insertmanyvalues 24/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 24/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine [insertmanyvalues 25/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 25/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine [insertmanyvalues 26/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/terms-of-service', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 26/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/terms-of-service', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine [insertmanyvalues 27/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/account/register', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 27/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/account/register', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine [insertmanyvalues 28/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/sleek-monochrome-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 28/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/sleek-monochrome-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine [insertmanyvalues 29/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 29/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine [insertmanyvalues 30/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/faqs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 30/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/faqs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,923 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,923 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine [insertmanyvalues 31/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 31/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine [insertmanyvalues 32/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-neon-attack-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 32/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-neon-attack-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine [insertmanyvalues 33/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-yoga-lite-leggings-black', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 33/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-yoga-lite-leggings-black', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine [insertmanyvalues 34/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/flex-on-quarter-zip-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 34/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/flex-on-quarter-zip-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine [insertmanyvalues 35/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 35/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine [insertmanyvalues 36/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Sports+Bra', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 36/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Sports+Bra', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine [insertmanyvalues 37/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/crop-tops', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 37/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/crop-tops', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine [insertmanyvalues 38/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 38/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,924 INFO sqlalchemy.engine.Engine [insertmanyvalues 39/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,924 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 39/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine [insertmanyvalues 40/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 40/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine [insertmanyvalues 41/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-ribbed-tights', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 41/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-ribbed-tights', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine [insertmanyvalues 42/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/bottom-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 42/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/bottom-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine [insertmanyvalues 43/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 43/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine [insertmanyvalues 44/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/wishlist', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 44/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/wishlist', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine [insertmanyvalues 45/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/all', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 45/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/collections/all', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine [insertmanyvalues 46/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 46/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/policies/refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine [insertmanyvalues 47/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 47/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,925 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,925 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:39:31,926 INFO sqlalchemy.engine.Engine [insertmanyvalues 48/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/shipping', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,926 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 48/48 (ordered; batch not supported)] ('208378df-fasdf32-sdfgcfeddcd', 'https://drynotch.com', 'https://drynotch.com/pages/shipping', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:39:31,926 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 20:39:31,926 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 20:39:31,939 - app.utils.website_url_processor - INFO - Successfully stored 48 URLs for scrape_request_ref_id: 208378df-fasdf32-sdfgcfeddcd
2025-07-29 20:39:31,940 INFO sqlalchemy.engine.Engine INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 20:39:31,940 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 20:39:31,940 INFO sqlalchemy.engine.Engine [generated in 0.00020s] ('https://drynotch.com', '208378df-fasdf32-sdfgcfeddcd', None, None, None, None, None, '2025-07-29T20:39:31.939654Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 20:39:31,940 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('https://drynotch.com', '208378df-fasdf32-sdfgcfeddcd', None, None, None, None, None, '2025-07-29T20:39:31.939654Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 20:39:31,941 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 20:39:31,941 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 20:39:31,944 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:39:31,944 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:39:31,945 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 20:39:31,945 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 20:39:31,945 INFO sqlalchemy.engine.Engine [generated in 0.00017s] (32,)
2025-07-29 20:39:31,945 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] (32,)
2025-07-29 20:39:31,946 - app.routers.mcc_analysis - INFO - Created new MCC analysis with ID 32
2025-07-29 20:39:31,946 - app.routers.mcc_analysis - INFO - === STARTING ASYNC PROCESSING for analysis 32 ===
2025-07-29 20:39:31,946 - app.routers.mcc_analysis - INFO - Async processing started for analysis 32
2025-07-29 20:39:31,946 - app.routers.mcc_analysis - INFO - Task will process: website=https://drynotch.com, scrape_ref=208378df-fasdf32-sdfgcfeddcd
2025-07-29 20:39:31,946 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 20:39:31,946 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-29 20:39:31,965 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-7' coro=<run_mcc_analysis_task() done, defined at /home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/routers/mcc_analysis.py:20> exception=SyntaxError("'await' outside async function", ('/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/services/mcc_service.py', 3191, 13, '            await self.handle_analysis_error(e, "site_unreachable")\n', 3191, 68))>
Traceback (most recent call last):
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/routers/mcc_analysis.py", line 21, in run_mcc_analysis_task
    from app.services.mcc_service import MccClassificationService
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/services/mcc_service.py", line 3191
    await self.handle_analysis_error(e, "site_unreachable")
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
SyntaxError: 'await' outside async function
[2025-07-29 20:39:31][request_middleware][NO_REF] INFO: Request completed: POST /mcc-analysis/ - 200
{
  "request_id": "f2b52979-88ef-4082-8cf2-c8c301d26568",
  "status_code": 200,
  "response_time_ms": 66.07
}
INFO:     127.0.0.1:58066 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
